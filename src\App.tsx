import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/Layout';
import SkillsList from './pages/SkillsList';
import SkillForm from './components/SkillForm';

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Navigate to="/skills" replace />} />
          <Route path="/skills" element={<SkillsList />} />
          <Route path="/skills/create" element={<SkillForm />} />
          <Route path="/skills/edit/:id" element={<SkillForm />} />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;