import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Save, Users } from 'lucide-react';
import { roleService } from '../services/roleService';
import { skillService } from '../services/skillService';
import { Role, RoleSkill, ProficiencyLevel } from '../types/role';
import { Skill } from '../types/skill';
import SkillsSelector from './SkillsSelector';
import ProficiencySelector from './ProficiencySelector';
import CertificationsManager from './CertificationsManager';

const RoleForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditing = Boolean(id);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    skills: [] as RoleSkill[]
  });
  const [selectedSkillIds, setSelectedSkillIds] = useState<string[]>([]);
  const [availableSkills, setAvailableSkills] = useState<Skill[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [initialLoading, setInitialLoading] = useState(isEditing);

  useEffect(() => {
    loadSkills();
    if (isEditing && id) {
      loadRole(id);
    }
  }, [isEditing, id]);

  const loadSkills = async () => {
    try {
      const skills = await skillService.getAllSkills();
      setAvailableSkills(skills);
    } catch (err) {
      console.error('Error loading skills:', err);
    }
  };

  const loadRole = async (roleId: string) => {
    try {
      setInitialLoading(true);
      const role = await roleService.getRoleById(roleId);
      if (role) {
        setFormData({
          name: role.name,
          description: role.description,
          skills: role.skills || []
        });
        setSelectedSkillIds(role.skills?.map(s => s.skillId) || []);
      } else {
        setError('Role not found');
      }
    } catch (err) {
      setError('Failed to load role');
      console.error('Error loading role:', err);
    } finally {
      setInitialLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.description.trim()) {
      setError('Please fill in all required fields');
      return;
    }

    if (formData.skills.length === 0) {
      setError('Please add at least one skill');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const roleData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        skills: formData.skills
      };

      if (isEditing && id) {
        await roleService.updateRole(id, roleData);
      } else {
        await roleService.createRole(roleData);
      }

      navigate('/roles');
    } catch (err) {
      setError(isEditing ? 'Failed to update role' : 'Failed to create role');
      console.error('Error saving role:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSkillsChange = (skillIds: string[]) => {
    setSelectedSkillIds(skillIds);
    
    // Update formData.skills to match selected skills
    const updatedSkills = skillIds.map(skillId => {
      const existingSkill = formData.skills.find(s => s.skillId === skillId);
      const skill = availableSkills.find(s => s.id === skillId);
      
      return existingSkill || {
        skillId,
        skillName: skill?.name || '',
        proficiency: 'Beginner' as ProficiencyLevel,
        certifications: []
      };
    });
    
    setFormData(prev => ({
      ...prev,
      skills: updatedSkills
    }));
  };

  const updateSkillProficiency = (skillId: string, proficiency: ProficiencyLevel) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.map(skill =>
        skill.skillId === skillId ? { ...skill, proficiency } : skill
      )
    }));
  };

  const updateSkillCertifications = (skillId: string, certifications: any[]) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.map(skill =>
        skill.skillId === skillId ? { ...skill, certifications } : skill
      )
    }));
  };

  if (initialLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <button
          onClick={() => navigate('/roles')}
          className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4 transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Roles
        </button>
        <div className="flex items-center">
          <Users className="h-8 w-8 text-blue-600 mr-3" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {isEditing ? 'Edit Role' : 'Create New Role'}
            </h1>
            <p className="mt-2 text-gray-600">
              {isEditing ? 'Update the role information below' : 'Define a new role with required skills and proficiencies'}
            </p>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information */}
        <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-6 space-y-6">
          <h2 className="text-lg font-semibold text-gray-900">Basic Information</h2>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Role Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="e.g., Senior Software Engineer"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Role Description <span className="text-red-500">*</span>
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={4}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Describe the role responsibilities and requirements..."
              required
            />
          </div>
        </div>

        {/* Skills Selection */}
        <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">Skills & Proficiencies</h2>
          
          <SkillsSelector
            selectedSkillIds={selectedSkillIds}
            onSkillsChange={handleSkillsChange}
            className="mb-8"
          />

          {/* Selected Skills with Proficiency and Certifications */}
          {formData.skills.length > 0 && (
            <div className="space-y-6">
              <h3 className="text-md font-medium text-gray-900">Configure Skills</h3>
              {formData.skills.map((roleSkill) => (
                <div key={roleSkill.skillId} className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                  <h4 className="text-lg font-medium text-gray-900 mb-4">{roleSkill.skillName}</h4>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <ProficiencySelector
                        selectedProficiency={roleSkill.proficiency}
                        onProficiencyChange={(proficiency) => updateSkillProficiency(roleSkill.skillId, proficiency)}
                      />
                    </div>
                    
                    <div>
                      <CertificationsManager
                        certifications={roleSkill.certifications}
                        onCertificationsChange={(certifications) => updateSkillCertifications(roleSkill.skillId, certifications)}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Save className="h-5 w-5 mr-2" />
            )}
            {loading ? 'Saving...' : (isEditing ? 'Update Role' : 'Create Role')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default RoleForm;
